{"name": "find-cache-dir", "version": "3.3.2", "description": "Finds the common standard cache directory", "license": "MIT", "repository": "avajs/find-cache-dir", "funding": "https://github.com/avajs/find-cache-dir?sponsor=1", "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava"}, "files": ["index.js"], "keywords": ["cache", "directory", "dir", "caching", "find", "search"], "dependencies": {"commondir": "^1.0.1", "make-dir": "^3.0.2", "pkg-dir": "^4.1.0"}, "devDependencies": {"ava": "^2.4.0", "coveralls": "^3.0.9", "del": "^4.0.0", "nyc": "^15.0.0", "tempy": "^0.4.0", "xo": "^0.25.3"}, "nyc": {"reporter": ["lcov", "text"]}}