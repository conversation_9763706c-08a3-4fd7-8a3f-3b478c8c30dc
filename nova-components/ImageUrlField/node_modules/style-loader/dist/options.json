{"type": "object", "properties": {"injectType": {"description": "Allows to setup how styles will be injected into DOM (https://github.com/webpack-contrib/style-loader#injecttype).", "enum": ["styleTag", "singletonStyleTag", "lazyStyleTag", "lazySingletonStyleTag", "linkTag"]}, "attributes": {"description": "Adds custom attributes to tag (https://github.com/webpack-contrib/style-loader#attributes).", "type": "object"}, "insert": {"description": "Inserts `<style>`/`<link>` at the given position (https://github.com/webpack-contrib/style-loader#insert).", "anyOf": [{"type": "string"}, {"instanceof": "Function"}]}, "base": {"description": "Sets module ID base for DLLPlugin (https://github.com/webpack-contrib/style-loader#base).", "type": "number"}, "esModule": {"description": "Use the ES modules syntax (https://github.com/webpack-contrib/css-loader#esmodule).", "type": "boolean"}, "modules": {"type": "object", "additionalProperties": false, "properties": {"namedExport": {"description": "Enables/disables ES modules named export for locals (https://webpack.js.org/plugins/mini-css-extract-plugin/#namedexport).", "type": "boolean"}}}}, "additionalProperties": false}