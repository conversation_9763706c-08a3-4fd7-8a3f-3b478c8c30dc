<a name="3.0.0"></a>
# 3.0.0 (2019-05-12)

* build: bye bower ([4acc7c0](https://github.com/kikobeats/emojis-list/commit/4acc7c0))
* build: remove gulp and bower ([12c83f3](https://github.com/kikobeats/emojis-list/commit/12c83f3))
* build: update meta ([de14b12](https://github.com/kikobeats/emojis-list/commit/de14b12))
* moar emojis ([b4a153f](https://github.com/kikobeats/emojis-list/commit/b4a153f))
* chore: drop support for Node.js 0.10 ([20ed4f1](https://github.com/kikobeats/emojis-list/commit/20ed4f1))


### BREAKING CHANGE

* This module no longer supports Node.js 0.10


<a name="2.1.0"></a>
# 2.1.0 (2016-10-03)

* Fetch new emoji ([7dbe84d](https://github.com/kikobeats/emojis-list/commit/7dbe84d))



<a name="2.0.1"></a>
## 2.0.1 (2016-05-12)

* Fix typo ([3808909](https://github.com/kikobeats/emojis-list/commit/3808909))



<a name="2.0.0"></a>
# 2.0.0 (2016-05-12)

* Add update script ([f846dd6](https://github.com/kikobeats/emojis-list/commit/f846dd6))
* Block dependencies in last version ([1d9e0a5](https://github.com/kikobeats/emojis-list/commit/1d9e0a5))
* Extract main file name ([9ffe7bb](https://github.com/kikobeats/emojis-list/commit/9ffe7bb))
* Remove unnecessary files ([4c34729](https://github.com/kikobeats/emojis-list/commit/4c34729))
* Update docs, special webpack setup is not necessary ([c4aefe9](https://github.com/kikobeats/emojis-list/commit/c4aefe9))
* Update example ([1e2ae03](https://github.com/kikobeats/emojis-list/commit/1e2ae03))
* Update how to generate emojis array ([b56bad9](https://github.com/kikobeats/emojis-list/commit/b56bad9))
* Update main file based in the new interface ([996fccb](https://github.com/kikobeats/emojis-list/commit/996fccb))



<a name="1.0.3"></a>
## 1.0.3 (2016-05-12)

* Add standard as linter ([5e939d6](https://github.com/kikobeats/emojis-list/commit/5e939d6))
* Change interface ([16bc0c0](https://github.com/kikobeats/emojis-list/commit/16bc0c0))
* Generate emoji file ([fbcf8e9](https://github.com/kikobeats/emojis-list/commit/fbcf8e9))
* Remove unnecessary special doc ([2b12bec](https://github.com/kikobeats/emojis-list/commit/2b12bec))
* chore(package): update browserify to version 13.0.1 ([e2c98bf](https://github.com/kikobeats/emojis-list/commit/e2c98bf))
* chore(package): update gulp-header to version 1.8.1 ([28de793](https://github.com/kikobeats/emojis-list/commit/28de793))



<a name="1.0.2"></a>
## 1.0.2 (2016-05-05)

* fixed #2 ([9a6abe7](https://github.com/kikobeats/emojis-list/commit/9a6abe7)), closes [#2](https://github.com/kikobeats/emojis-list/issues/2)
* Fomar using standard ([5202f9f](https://github.com/kikobeats/emojis-list/commit/5202f9f))
* Update badge ([53fad9b](https://github.com/kikobeats/emojis-list/commit/53fad9b))



<a name="1.0.1"></a>
## 1.0.1 (2016-04-13)

* lock versions ([4a5d82e](https://github.com/kikobeats/emojis-list/commit/4a5d82e))
* setup devDependencies ([d1de0fc](https://github.com/kikobeats/emojis-list/commit/d1de0fc))
* update bumped ([9941038](https://github.com/kikobeats/emojis-list/commit/9941038))
* Update package.json ([6c14b74](https://github.com/kikobeats/emojis-list/commit/6c14b74))
* Update README.md ([1d9beeb](https://github.com/kikobeats/emojis-list/commit/1d9beeb))
* Update README.md ([73f215e](https://github.com/kikobeats/emojis-list/commit/73f215e))
* Update tests ([a94f7dc](https://github.com/kikobeats/emojis-list/commit/a94f7dc))



<a name="1.0.0"></a>
# 1.0.0 (2015-05-12)

* first commit ([a65b79d](https://github.com/kikobeats/emojis-list/commit/a65b79d))
* updated ([9f0564c](https://github.com/kikobeats/emojis-list/commit/9f0564c))



