{"name": "webpack-notifier", "version": "1.15.0", "description": "webpack + node-notifier = build status system notifications", "main": "index.js", "types": "./index.d.ts", "files": ["index.js", "index.d.ts", "logo.png"], "scripts": {"prepublishOnly": "npm run lint", "lint": "eslint index.js", "test": "jest", "test:coverage": "jest --coverage", "tsd": "tsd"}, "husky": {"hooks": {"pre-commit": "npm run lint"}}, "keywords": ["webpack", "notify", "notification", "node-notifier", "notifier", "build"], "repository": {"type": "git", "url": "https://github.com/Turbo87/webpack-notifier.git"}, "author": "<PERSON> <<EMAIL>>", "license": "ISC", "dependencies": {"node-notifier": "^9.0.0", "strip-ansi": "^6.0.0"}, "devDependencies": {"@types/jest": "^26.0.15", "@types/node-notifier": "^8.0.1", "@types/semver": "^7.3.8", "@types/webpack": ">4.41.31", "eslint": "^7.14.0", "eslint-config-airbnb-base": "^14.2.1", "husky": "^4.3.0", "jest": "^24.9.0", "memfs": "^3.2.0", "semver": "^7.3.2", "ts-jest": "^24.3.0", "tsd": "^0.17.0", "typescript": "^3.9.7", "webpack-1": "npm:webpack@1", "webpack-2": "npm:webpack@2", "webpack-3": "npm:webpack@3", "webpack-4": "npm:webpack@4", "webpack-5": "npm:webpack@5", "webpack-latest": "npm:webpack@latest"}, "peerDependencies": {"@types/webpack": ">4.41.31"}, "peerDependenciesMeta": {"@types/webpack": {"optional": true}}}