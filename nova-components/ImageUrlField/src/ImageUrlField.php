<?php

namespace Capitalc\ImageUrlField;

use Laravel\Nova\Fields\Field;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Exception;

class ImageUrlField extends Field
{
    /**
     * The field's component.
     *
     * @var string
     */
    public $component = 'image-url-field';

    /**
     * Create a new field.
     *
     * @param  string  $name
     * @param  string|callable|null  $attribute
     * @param  callable|null  $resolveCallback
     * @return void
     */
    public function __construct($name, $attribute = null, ?callable $resolveCallback = null)
    {
        parent::__construct($name, $attribute, $resolveCallback);

        $this->withMeta([
            'placeholder' => 'https://example.com/image1.jpg, https://example.com/image2.png',
        ]);

        // This field doesn't store data directly, so we'll use a virtual attribute
        $this->attribute = $attribute ?: 'image_urls';
    }

    /**
     * Hydrate the given attribute on the model based on the incoming request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  string  $requestAttribute
     * @param  object  $model
     * @param  string  $attribute
     * @return void
     */
    protected function fillAttributeFromRequest(NovaRequest $request, $requestAttribute, $model, $attribute)
    {
        if ($request->exists($requestAttribute) && !empty($request[$requestAttribute])) {
            $urls = $this->parseUrls($request[$requestAttribute]);
            
            if (!empty($urls)) {
                $this->processImageUrls($model, $urls);
            }
        }
    }

    /**
     * Parse comma-separated URLs from the input.
     *
     * @param  string  $input
     * @return array
     */
    protected function parseUrls($input)
    {
        $urls = array_map('trim', explode(',', $input));
        return array_filter($urls, function ($url) {
            return !empty($url) && filter_var($url, FILTER_VALIDATE_URL);
        });
    }

    /**
     * Process and attach images from URLs to the model.
     *
     * @param  object  $model
     * @param  array  $urls
     * @return void
     */
    protected function processImageUrls($model, $urls)
    {
        $allowedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        
        foreach ($urls as $url) {
            try {
                // Validate URL format
                $validator = Validator::make(['url' => $url], [
                    'url' => 'required|url|regex:/\.(jpg|jpeg|png|gif|webp)$/i'
                ]);

                if ($validator->fails()) {
                    Log::warning("Invalid image URL format: {$url}");
                    continue;
                }

                // Add media from URL using Laravel Media Library
                $mediaItem = $model->addMediaFromUrl($url)
                    ->toMediaCollection('media');

                Log::info("Successfully added image from URL: {$url}", [
                    'media_id' => $mediaItem->id,
                    'model_type' => get_class($model),
                    'model_id' => $model->id
                ]);

            } catch (Exception $e) {
                Log::error("Failed to add image from URL: {$url}", [
                    'error' => $e->getMessage(),
                    'model_type' => get_class($model),
                    'model_id' => $model->id
                ]);
            }
        }
    }

    /**
     * Resolve the field's value.
     *
     * @param  mixed  $resource
     * @param  string|null  $attribute
     * @return mixed
     */
    public function resolve($resource, $attribute = null)
    {
        // This field doesn't display existing data, it's only for input
        return '';
    }

    /**
     * Set the placeholder text for the field.
     *
     * @param  string  $placeholder
     * @return $this
     */
    public function placeholder($placeholder)
    {
        return $this->withMeta(['placeholder' => $placeholder]);
    }

    /**
     * Set help text for the field.
     *
     * @param  string  $helpText
     * @return $this
     */
    public function help($helpText)
    {
        return $this->withMeta(['helpText' => $helpText]);
    }
}
